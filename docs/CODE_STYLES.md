# Code Style Guidelines

## Formatting Rules

### Indentation & Spacing

-   Use 4 spaces for indentation, not tabs
-   No enforced maximum line length (printWidth: false)
-   Remove trailing whitespace
-   Add a blank line at end of files
-   Use LF (`\n`) line endings, not CRLF
-   Place spaces inside object braces: `{ like: this }`
-   Add space before opening braces: `function name() {`
-   Enable block spacing for consistent formatting

### Punctuation & Symbols

-   No semicolons at the end of statements
-   Use single quotes (`'`) for strings in JS/TS
-   Use double quotes (`"`) for JSX attributes
-   Use trailing commas in ES5 style:
    -   Always for multiline arrays and objects
    -   Never for imports/exports
    -   Never for function parameters
-   Always use parentheses with arrow functions, even for single parameters
-   Use "one true brace style" (1tbs): opening brace on same line
-   Closing bracket on new line (bracketSameLine: false)

### Line Breaking & Padding

-   Add blank lines before and after major code blocks
-   Add blank line before `return` statements
-   Always blank line before and after: `class`, `interface`, `function`, `if`, `for`, `while`, `switch`, `try`
-   No blank lines between `case` statements in `switch`
-   Consistent object and array formatting based on complexity

## JavaScript & TypeScript

### Import Organization

Group imports in strict order:

1. Node.js built-in modules (with `node:` prefix)
2. External libraries (alphabetical)
3. Side-effect imports (`import 'module'`)
4. Internal modules (by proximity: `../`, `./`)

```typescript
// 1. Node.js built-ins
import { existsSync } from 'node:fs'
import process from 'node:process'

// 2. External libraries
import { DataSource } from 'typeorm'
import { tap } from '@kdt310722/utils/function'

// 3. Side-effects
import 'reflect-metadata'

// 4. Internal modules
import { config } from '../config'
import { logger } from './logger'
```

### Import Rules

-   Remove unused imports automatically
-   Keep import statements at the top of the file
-   Keep import in one line for each import (enforced by custom rule)
-   Use type-only imports: `import type { Type }` for types only
-   Prefer inline type imports: `import { type Type, value }` when mixing
-   No import type side effects

### Type Safety

-   Use TypeScript's strict type checking
-   Define clear types for functions and variables
-   Only specify return types for complex types or when the return type is not obvious from the code
-   Add accessibility modifier on class properties and methods
-   Prefer using interface instead of type for TypeScript interfaces
-   No `any` type (prefer `unknown`)
-   Use utility types for type manipulation (`Pick`, `Omit`, `Partial`)

### Function & Variable Rules

-   Keep return statements clear and explicit
-   Function length: maximum 30 lines recommended
-   Nesting depth: maximum 3 levels deep
-   Unused parameters: prefix with underscore (`_error`, `_unused`)
-   Use descriptive names that indicate purpose

## Naming Conventions

### Standard Conventions

-   Variables and functions: camelCase
-   Classes and Components: PascalCase
-   Global constants: UPPERCASE_SNAKE_CASE
-   Files: kebab-case for regular files, PascalCase for component files
-   Directories: kebab-case grouped by functionality

## Code Organization

### File Structure

1. Imports (following grouping rules)
2. Type definitions and interfaces
3. Constants and configuration
4. Implementation (functions, classes)
5. Exports (prefer named exports, organized alphabetically)

### Class Organization

Structure class members in this order:

1. Public properties
2. Protected properties
3. Private properties (prefer `#privateField` syntax for private fields)
4. Constructor
5. Static methods
6. Instance methods (public → protected → private)

```typescript
export class UserService {
    public readonly config: Config
    protected state: string
    #secretKey: string

    public constructor(config: Config) {
        this.config = config
        this.state = 'initialized'
        this.#secretKey = generateSecret()
    }

    public static create(config: Config): UserService {
        return new UserService(config)
    }

    public getUser(): User {
        return this.#fetchUser()
    }

    #fetchUser(): User {
        // private implementation
    }
}
```

## Custom ESLint Rules

### kdt/arrow-empty-body-newline

Prevents newlines in empty arrow function bodies.

```typescript
// ❌ Incorrect
const emptyFn = () => {}

// ✅ Correct
const emptyFn = () => {}
```

### kdt/import-single-line

Enforces single-line import statements.

```typescript
// ❌ Incorrect
import { someFunction, anotherFunction } from 'module'

// ✅ Correct
import { someFunction, anotherFunction } from 'module'
```

### kdt/object-curly-newline

Enhanced object formatting with minimum properties threshold.

```typescript
// Objects with fewer properties stay single line
const small = { a: 1, b: 2 }

// Objects exceeding threshold use multi-line format
const large = {
    property1: 'value1',
    property2: 'value2',
    property3: 'value3',
}
```

## Error Handling

### Error Patterns

-   Catch specific errors, not all errors
-   Include useful information in error messages
-   Fail fast: detect and report errors as early as possible
-   Use logical error hierarchy
-   Create immutable error objects

## Performance Guidelines

### Optimization Principles

-   Lazy load resources when needed
-   Optimize loops: avoid unnecessary nested loops
-   Use async/await for I/O operations
-   Manage memory carefully, especially in closures
-   Implement code splitting and tree shaking
-   Use BigInt for large number calculations (Solana amounts)
-   Prefer `const` assertions for immutable data

### Common Usage Patterns

-   Apply utility functions before writing custom implementations
-   Prefer using util functions in `@kdt310722/utils` when possible
-   Use functional composition patterns with pipe functions
-   Implement proper error boundaries and fallbacks

## Project-Specific Rules

### Common Rule Overrides

Different projects may have specific ESLint rule overrides:

**farmer, json-rpc-proxy, solana-transaction-sender:**

-   `'ts/prefer-promise-reject-errors': 'off'` - Allow non-Error rejections

**pumpfun-sdk, solana-grpc-client:**

-   `'sonarjs/no-selector-parameter': 'off'` - Allow selector parameters

**solana-transaction-sender:**

-   `'sonarjs/no-dead-store': 'off'` - Allow unused assignments

### File Ignore Patterns

Standard ignore patterns across projects:

-   `migrations` - Database migrations
-   `generated` - Auto-generated code
-   `dist` - Build output
-   `**/protos` - Protocol buffer definitions
-   `README.md` - Documentation files

### TypeScript Configuration Variations

**Base projects:**

```json
{
    "extends": "@kdt310722/tsconfig"
}
```

**TypeORM projects:**

```json
{
    "extends": "@kdt310722/tsconfig",
    "compilerOptions": {
        "emitDecoratorMetadata": true,
        "experimentalDecorators": true
    }
}
```

## Code Reuse Principles

### Reuse Guidelines

-   Follow Open/Closed principle: extend without modifying
-   Prefer composition over inheritance
-   Extract reusable logic into utils/composables
-   Create wrappers for existing components when needed
-   Maintain backward compatibility

### Avoiding Duplication

-   Follow DRY (Don't Repeat Yourself) principle
-   Apply Rule of Three: if code is copy-pasted 3 times, extract it
-   Search for similar code before implementing new features
-   Use utility types and helper functions consistently

## Tools & Development Setup

### Required Dependencies

-   **ESLint**: `@kdt310722/eslint-config`
-   **TypeScript**: `@kdt310722/tsconfig`
-   **Package Manager**: `pnpm` (enforced via `only-allow`)
-   **Git Hooks**: `simple-git-hooks` with `lint-staged`

### Git Hooks Configuration

```json
{
    "simple-git-hooks": {
        "commit-msg": "npx --no -- commitlint --edit ${1}",
        "pre-commit": "npx lint-staged"
    },
    "lint-staged": {
        "*": "eslint --fix"
    }
}
```

### Build Tools

-   **tsup**: For library builds with dual CJS/ESM output
-   **tsx**: For development execution
-   **TypeORM**: For database entities with decorators

### IDE Configuration

-   Enable ESLint auto-fix on save
-   Configure TypeScript strict mode
-   Use consistent line endings (LF)
-   Set tab size to 4 spaces
-   Trim trailing whitespace automatically

## Code Generation Guidelines

### Structure Principles

-   Simple and clear: each function/class does one specific task
-   Readability over brevity
-   Function length: maximum 30 lines
-   Nesting depth: maximum 3 levels deep
-   Separate business logic from UI concerns
-   Prioritize consistency with existing codebase

### Quality Standards

-   Follow project-specific ESLint configurations
-   Maintain comprehensive test coverage where applicable
-   Use descriptive variable and function names
-   Implement proper error handling and logging
-   Follow established patterns for database entities and API interactions

---

_This comprehensive style guide is enforced through automated tooling and should be followed consistently across all projects. The rules are designed to ensure code readability, maintainability, and consistency across the entire codebase._
