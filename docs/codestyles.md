# Code Style Guide

## Table of Contents

-   [Overview](#overview)
-   [ESLint Configuration](#eslint-configuration)
-   [TypeScript Configuration](#typescript-configuration)
-   [Formatting Rules](#formatting-rules)
-   [Naming Conventions](#naming-conventions)
-   [Code Structure](#code-structure)
-   [Import Organization](#import-organization)
-   [Function Declarations](#function-declarations)
-   [Class Structure](#class-structure)
-   [Object and Interface Formatting](#object-and-interface-formatting)
-   [Erro<PERSON> Handling](#error-handling)
-   [Best Practices](#best-practices)
-   [Tools and Configuration](#tools-and-configuration)

## Overview

This document outlines the comprehensive code style standards used across all projects. The standards are enforced through a shared ESLint configuration preset `@kdt310722/eslint-config` and consistent TypeScript configurations.

## ESLint Configuration

### Base Configuration

All projects use the shared ESLint config:

```javascript
export default import('@kdt310722/eslint-config').then((m) =>
    m.defineFlatConfig(
        {},
        {
            ignores: ['migrations', 'generated', 'dist'],
        }
    )
)
```

### Key ESLint Rules

-   **Stylistic Rules**: Enforced through `@stylistic/eslint-plugin`
-   **TypeScript Rules**: Strict type checking with `@typescript-eslint`
-   **Import Rules**: Organized imports with `eslint-plugin-import-x`
-   **Custom Rules**: Three custom rules for specific formatting needs

## Formatting Rules

### Indentation and Spacing

-   **Indent**: 4 spaces (no tabs)
-   **Line Width**: No enforced max width (printWidth: false)
-   **End of Line**: Unix (LF)

### Quotes and Semicolons

-   **Quotes**: Single quotes for strings
-   **JSX Quotes**: Double quotes for JSX attributes
-   **Semicolons**: No semicolons (semi: false)
-   **Quote Props**: Consistent quoting for object properties

### Braces and Brackets

-   **Brace Style**: 1tbs (one true brace style)
-   **Block Spacing**: Enabled
-   **Bracket Same Line**: false (closing bracket on new line)

### Comma and Trailing Commas

-   **Comma Dangle**: ES5 style (always for arrays/objects, never for imports/exports)
-   **Arrow Parens**: Always use parentheses around arrow function parameters

## Naming Conventions

### Variables and Functions

```typescript
// camelCase for variables and functions
const userName = 'john'
const isActive = true

function getUserData() {
    return userData
}

// Arrow functions
const processData = (data: string) => {
    return data.trim()
}
```

### Constants

```typescript
// SCREAMING_SNAKE_CASE for constants
export const MAX_VALID_SLOT_GAP = 100
export const SOL_DECIMALS = 9
export const PUMPFUN_TOKEN_DECIMALS = 6
```

### Types and Interfaces

```typescript
// PascalCase for types and interfaces
interface UserData {
    name: string
    email: string
}

type ResponseStatus = 'success' | 'error'
```

### Classes

```typescript
// PascalCase for classes
export class BuyTransaction {
    public declare id: number
    private declare internalData: string
}
```

## Code Structure

### File Organization

```typescript
// 1. Imports (external libraries first, then internal)
import { tap } from '@kdt310722/utils/function'
import { waterfall } from '@kdt310722/utils/promise'
import 'reflect-metadata'

// 2. Internal imports (grouped by functionality)
import { initializeDatabase } from './core/database'
import { logger } from './core/logger'

// 3. Type definitions
export interface ConfigOptions {
    database: DatabaseConfig
    logger: LoggerConfig
}

// 4. Constants
export const DEFAULT_TIMEOUT = 5000

// 5. Implementation
export function initialize() {
    // implementation
}
```

### Padding Lines Between Statements

-   Always blank line before and after: `class`, `interface`, `function`, `if`, `for`, `while`, `switch`, `try`
-   Always blank line before `return` statements
-   No blank lines between `case` statements in `switch`

## Import Organization

### Import Grouping

```typescript
// External libraries
import { DataSource } from 'typeorm'
import type { Nullable } from '@kdt310722/utils/common'

// Internal modules (relative imports)
import { DatabaseLogger } from '../utils/database/logger'
import { config } from '../config'
import { createChildLogger } from './logger'
```

### Import Rules

-   Use type-only imports when importing only types: `import type { Type } from 'module'`
-   Prefer inline type imports: `import { type Type, value } from 'module'`
-   Single line imports (enforced by custom rule)
-   No unused imports (automatically removed)

## Function Declarations

### Function Signatures

```typescript
// Regular function declaration
export async function initializeDatabase() {
    const complete = databaseLogger.createLoading().start('Initializing database...')

    return skipSlowQueryLogging(() => database.initialize()).then(() => {
        complete('Database is initialized!')
    })
}

// Arrow function with explicit return type
export const createChildLogger = (name: string): Logger => {
    return logger.child({ name })
}

// Complex function with multiple parameters
export function calculateTokenOut(
    bondingCurve: Pick<BondingCurve, 'virtualSolReserves' | 'virtualTokenReserves' | 'realTokenReserves'>,
    solIn: bigint,
    feeBasisPoints: bigint,
    creatorFeeBasisPoints: bigint
): bigint {
    if (solIn === 0n || bondingCurve.realTokenReserves === 0n) {
        return 0n
    }

    // implementation
}
```

### Return Type Placement

-   Return types are placed on the same line as the function signature
-   For complex types, use type aliases to keep signatures readable
-   Always specify return types for public functions

## Class Structure

### Class Member Organization

```typescript
export class BuyTransaction {
    // 1. Primary key
    @PrimaryGeneratedColumn()
    public declare id: number

    // 2. Indexed columns
    @Index()
    @Column()
    public declare slot: number

    // 3. Regular columns
    @Column('varchar')
    public declare signature: Signature

    // 4. Relationships
    @OneToOne(() => SellTransaction)
    @JoinColumn()
    public declare sellTransaction: Relation<SellTransaction>

    // 5. Timestamps
    @CreateDateColumn()
    public declare createdAt: Date

    // 6. Static methods
    public static fromBuyResult(token: BuyToken, result: ConfirmResultSuccess): BuyTransaction {
        const entity = new BuyTransaction()
        // implementation
        return entity
    }

    // 7. Instance methods
    public getDisplayName(): string {
        return `Transaction ${this.id}`
    }
}
```

### Access Modifiers

-   Always specify access modifiers explicitly: `public`, `private`, `protected`
-   Use `declare` for properties that are initialized by decorators (TypeORM entities)
-   Order: `public` → `protected` → `private`

## Object and Interface Formatting

### Object Literals

```typescript
// Single line for simple objects
const config = { host: 'localhost', port: 3000 }

// Multi-line for complex objects (enforced by custom rule)
const complexConfig = {
    database: {
        host: 'localhost',
        port: 5432,
        name: 'database',
    },
    redis: {
        host: 'localhost',
        port: 6379,
    },
}

// Consistent trailing commas
const array = ['item1', 'item2', 'item3']
```

### Interface Definitions

```typescript
export interface CreateBuyTransactionParams extends CreateTradeTransactionParams {
    tokenAccounts: Address[]
}

export interface CalculateVirtualReservesBeforeParams {
    virtualSolReserves: bigint
    virtualTokenReserves: bigint
    solAmount: bigint
    tokenAmount: bigint
    isBuy: boolean
}
```

## Error Handling

### Error Patterns

```typescript
// Explicit error handling
export async function initializeDatabase() {
    try {
        await database.initialize()
        logger.info('Database initialized successfully')
    } catch (error) {
        logger.error('Failed to initialize database', error)
        throw error
    }
}

// Promise-based error handling
const app = init
    .then(() => logger.info('Application initialized!'))
    .catch((error) => {
        logger.forceExit(1, 'fatal', 'Failed to initialize application', error)
    })
```

## Best Practices

### Type Safety

-   Use strict TypeScript configuration
-   Prefer `unknown` over `any`
-   Use type assertions sparingly and with proper type guards
-   Leverage utility types: `Pick`, `Omit`, `Partial`, etc.

### Performance

-   Use `BigInt` for large numbers (Solana amounts)
-   Prefer `const` assertions for immutable data
-   Use proper async/await patterns

### Code Organization

-   Export types and implementations from index files
-   Group related functionality in modules
-   Use barrel exports for clean import paths

## Tools and Configuration

### Required Tools

-   **ESLint**: `@kdt310722/eslint-config`
-   **TypeScript**: `@kdt310722/tsconfig`
-   **Prettier**: Integrated through ESLint formatters
-   **Git Hooks**: `simple-git-hooks` with `lint-staged`

### IDE Configuration

-   Enable ESLint auto-fix on save
-   Configure TypeScript strict mode
-   Use consistent line endings (LF)
-   Set tab size to 4 spaces

### Build Tools

-   **tsup**: For library builds
-   **tsx**: For development execution
-   **TypeORM**: For database entities with decorators

## Detailed ESLint Rules Analysis

### Stylistic Rules (@stylistic/eslint-plugin)

#### Line Breaking and Spacing Rules

```typescript
// @stylistic/padding-line-between-statements
// Always blank line before return
function processData(data: string) {
    const processed = data.trim()
    const validated = validate(processed)

    return validated // Always preceded by blank line
}

// Always blank line around classes and interfaces
interface UserConfig {
    name: string
    email: string
}

class UserService {
    private config: UserConfig

    constructor(config: UserConfig) {
        this.config = config
    }
}

// Always blank line around control structures
if (condition) {
    doSomething()
}

for (const item of items) {
    processItem(item)
}

while (isRunning) {
    checkStatus()
}
```

#### Object and Array Formatting

```typescript
// @stylistic/object-curly-newline - Custom rule implementation
// Single line for simple objects
const simple = { a: 1, b: 2 }

// Multi-line for complex objects
const complex = {
    database: {
        host: 'localhost',
        port: 5432,
    },
    cache: {
        ttl: 3600,
    },
}

// @stylistic/array-bracket-newline - Consistent formatting
const simpleArray = [1, 2, 3]
const complexArray = [
    { id: 1, name: 'first' },
    { id: 2, name: 'second' },
    { id: 3, name: 'third' },
]
```

#### Function Call Formatting

```typescript
// @stylistic/function-call-argument-newline - Consistent arguments
// Single line for simple calls
const result = calculate(a, b, c)

// Multi-line for complex calls
const transaction = createBuyTransaction({
    mint,
    bondingCurve,
    user,
    tokenAccounts,
    amount,
    slippage,
    feeRecipient,
    global,
})
```

### Custom Rules Analysis

#### 1. kdt/arrow-empty-body-newline

```typescript
// ❌ Incorrect - newline in empty arrow function body
const emptyFn = () => {}

// ✅ Correct - single line empty body
const emptyFn = () => {}
```

#### 2. kdt/import-single-line

```typescript
// ❌ Incorrect - line breaks in import
import { someFunction, anotherFunction } from 'module'

// ✅ Correct - single line import
import { someFunction, anotherFunction } from 'module'
```

#### 3. kdt/object-curly-newline

```typescript
// Enhanced object formatting with minimum properties threshold
// Objects with fewer properties stay on single line
const small = { a: 1, b: 2 }

// Objects exceeding threshold use multi-line format
const large = {
    property1: 'value1',
    property2: 'value2',
    property3: 'value3',
    property4: 'value4',
}
```

### TypeScript Specific Rules

#### Type Imports and Exports

```typescript
// @typescript-eslint/consistent-type-imports
// ✅ Correct - type-only imports
import type { Address, Signature } from '@solana/kit'
import { createTransaction } from '@solana/kit'

// ✅ Correct - inline type imports
import { type BondingCurve, calculateTokenOut } from './utils'

// @typescript-eslint/consistent-type-exports
// ✅ Correct - type-only exports
export type { UserConfig, DatabaseConfig }
export { UserService, DatabaseService }
```

#### Access Modifiers and Member Ordering

```typescript
// @typescript-eslint/explicit-member-accessibility
export class TransactionEntity {
    // Public properties first
    @PrimaryGeneratedColumn()
    public declare id: number

    @Column()
    public declare amount: bigint

    // Protected properties
    protected declare internalState: string

    // Private properties last
    private declare secretKey: string

    // Constructor
    public constructor(data: Partial<TransactionEntity>) {
        Object.assign(this, data)
    }

    // Public methods
    public getDisplayAmount(): string {
        return this.amount.toString()
    }

    // Protected methods
    protected validateState(): boolean {
        return this.internalState !== ''
    }

    // Private methods
    private encryptData(): void {
        // implementation
    }
}
```

#### Variable and Function Naming

```typescript
// @typescript-eslint/no-unused-vars with specific patterns
// ✅ Correct - unused parameters with underscore prefix
function processCallback(_error: Error, data: string): void {
    console.log(data)
}

// ✅ Correct - destructuring with ignored values
const [first, , third] = array
const { name, ..._ } = object
```

### Import Organization Patterns

#### External vs Internal Imports

```typescript
// 1. Node.js built-in modules
import { existsSync } from 'node:fs'
import { join } from 'node:path/posix'
import process from 'node:process'

// 2. External libraries (alphabetical)
import { DataSource } from 'typeorm'
import { tap } from '@kdt310722/utils/function'
import { waterfall } from '@kdt310722/utils/promise'

// 3. Side-effect imports
import 'reflect-metadata'

// 4. Internal modules (by proximity)
import { GLOB_SRC, GLOB_TS, GLOB_TSX } from '../globs'
import { parserTypescript, pluginImport, pluginTypescript } from '../plugins'
import type { FlatConfig } from '../types'
import { renameRules, toArray } from '../utils'
```

### Perfectionist Plugin Rules

#### Sorting Rules

```typescript
// perfectionist/sort-imports - Automatic import sorting
import { a, b, c } from 'module-a' // Alphabetical within groups
import { x, y, z } from 'module-b'

// perfectionist/sort-objects - Object key sorting
const config = {
    database: 'postgres',
    host: 'localhost',
    password: 'secret',
    port: 5432,
    username: 'admin',
}

// perfectionist/sort-interfaces - Interface member sorting
interface UserData {
    email: string // Alphabetical
    id: number
    name: string
    role: UserRole
}
```

### Error Handling Patterns

#### Promise Error Handling

```typescript
// Waterfall pattern for sequential initialization
const init = waterfall([initializeDatabase, initializeRpcClient, initializeWallet, initializeBuyer])

const app = init
    .then(() => logger.info('Application initialized!'))
    .catch((error) => {
        logger.forceExit(1, 'fatal', 'Failed to initialize application', error)
    })
```

#### Try-Catch with Logging

```typescript
export async function initializeDatabase() {
    const complete = databaseLogger.createLoading().start('Initializing database...')

    try {
        await skipSlowQueryLogging(() => database.initialize())
        complete('Database is initialized!')
    } catch (error) {
        complete.fail('Failed to initialize database')
        throw error
    }
}
```

### Performance and BigInt Patterns

#### BigInt Usage for Solana

```typescript
// Constants with BigInt
export const SOL_DECIMALS = 9
export const SOL_DENOMINATOR = 10 ** SOL_DECIMALS
export const PUMPFUN_TOKEN_PRICE_DECIMALS = 9n

// BigInt calculations
export function calculateTokenOut(
    bondingCurve: Pick<BondingCurve, 'virtualSolReserves' | 'virtualTokenReserves' | 'realTokenReserves'>,
    solIn: bigint,
    feeBasisPoints: bigint,
    creatorFeeBasisPoints: bigint
): bigint {
    if (solIn === 0n || bondingCurve.realTokenReserves === 0n) {
        return 0n
    }

    const { virtualSolReserves, virtualTokenReserves, realTokenReserves } = bondingCurve
    const totalFeeBasisPoints = feeBasisPoints + creatorFeeBasisPoints
    const inputAmount = (solIn * 10_000n) / (totalFeeBasisPoints + 10_000n)
    const tokensReceived = (inputAmount * virtualTokenReserves) / (virtualSolReserves + inputAmount)

    return BigIntMath.min(tokensReceived, realTokenReserves)
}
```

### Database Entity Patterns

#### TypeORM Entity Structure

```typescript
@Entity()
export class BuyTransaction {
    // Primary key always first
    @PrimaryGeneratedColumn()
    public declare id: number

    // Indexed columns for queries
    @Index()
    @Column()
    public declare slot: number

    @Index()
    @Column('varchar')
    public declare signature: Signature

    @Index()
    @Column('varchar')
    public declare mint: Address

    // Data columns
    @JsonColumn()
    public declare tokenInfo: Token

    @BigIntColumn()
    public declare amount: bigint

    // Relationships
    @OneToOne(() => SellTransaction, { nullable: true })
    @JoinColumn()
    public declare sellTransaction?: Relation<SellTransaction>

    // Timestamps always last
    @CreateDateColumn()
    public declare createdAt: Date

    @UpdateDateColumn()
    public declare updatedAt: Date

    // Static factory methods
    public static fromBuyResult(
        token: BuyToken,
        result: ConfirmResultSuccess,
        processTime: bigint,
        params: CreateBuyTransactionParams
    ): BuyTransaction {
        const entity = new BuyTransaction()
        const trade = this.getTradeEvent(result)

        // Property assignment
        entity.slot = result.slot
        entity.signature = result.signature
        entity.mint = token.mint

        return entity
    }
}
```

## Project-Specific Exceptions

### Common Rule Overrides

Different projects may have specific rule overrides based on their requirements:

```javascript
// farmer, json-rpc-proxy, solana-transaction-sender
{
    rules: {
        'ts/prefer-promise-reject-errors': 'off',  // Allow non-Error rejections
    },
}

// pumpfun-sdk, solana-grpc-client
{
    rules: {
        'sonarjs/no-selector-parameter': 'off',  // Allow selector parameters
    },
}

// solana-transaction-sender
{
    rules: {
        'sonarjs/no-dead-store': 'off',  // Allow unused assignments
    },
}
```

### File-Specific Ignores

```javascript
// Common ignore patterns across projects
{
    ignores: [
        'migrations',      // Database migrations
        'generated',       // Auto-generated code
        'dist',           // Build output
        '**/protos',      // Protocol buffer definitions
        'README.md',      // Documentation files
    ],
}
```

## Advanced Patterns

### Pipe Function Usage

```typescript
// Functional composition pattern
return pipe(
    createTransactionMessage({ version: 'legacy' }),
    (message) => setTransactionMessageFeePayer(user.address, message),
    (message) => appendTransactionMessageInstructions(instructions, message)
)
```

### Conditional Instruction Building

```typescript
export async function createBuyTransaction(params: CreateBuyTransactionParams) {
    const instructions: IInstruction[] = []
    const tokenAccount = await getAssociatedTokenAddress(mint, user.address)
    const isLegacy = isLegacyBondingCurve(bondingCurve)

    // Conditional instruction addition
    if (!tokenAccounts.includes(tokenAccount)) {
        instructions.push(
            getCreateAssociatedTokenInstruction({
                mint,
                owner: user.address,
                ata: tokenAccount,
                payer: user,
            })
        )
    }

    if (isLegacy) {
        instructions.push(createExtendAccountInstruction(user, params.bondingCurve))
    }

    instructions.push(getBuyInstruction(params))

    return createTransactionMessage(instructions)
}
```

### Utility Type Usage

```typescript
// Extensive use of utility types for type safety
export function calculateTokenOut(
    bondingCurve: Pick<BondingCurve, 'virtualSolReserves' | 'virtualTokenReserves' | 'realTokenReserves'>,
    solIn: bigint,
    feeBasisPoints: bigint,
    creatorFeeBasisPoints: bigint
): bigint

// Omit for excluding properties
export interface CreateBuyTransactionParams extends Omit<CreateTradeTransactionParams, 'direction'> {
    tokenAccounts: Address[]
}

// Partial for optional properties
public constructor(data: Partial<TransactionEntity>) {
    Object.assign(this, data)
}
```

### Configuration Patterns

#### Environment-Based Configuration

```typescript
// TypeScript configuration inheritance
{
    "extends": "@kdt310722/tsconfig",
    "compilerOptions": {
        "emitDecoratorMetadata": true,      // For TypeORM projects
        "experimentalDecorators": true
    },
    "ts-node": {
        "esm": true,
        "experimentalSpecifierResolution": "node",
        "transpileOnly": true
    }
}
```

#### Build Configuration

```typescript
// tsup.config.ts pattern
export default defineConfig({
    entry: ['src/index.ts'],
    format: ['cjs', 'esm'],
    dts: true,
    clean: true,
    splitting: false,
    sourcemap: true,
})
```

## Quality Assurance

### Git Hooks Integration

```json
{
    "simple-git-hooks": {
        "commit-msg": "npx --no -- commitlint --edit ${1}",
        "pre-commit": "npx lint-staged"
    },
    "lint-staged": {
        "*": "eslint --fix"
    }
}
```

### Continuous Integration

```yaml
# .github/workflows/autofix.yml
name: Autofix
on:
    push:
        branches: [main]
    pull_request:
        branches: [main]

jobs:
    autofix:
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@v4
            - uses: actions/setup-node@v4
              with:
                  node-version: '20'
            - run: pnpm install
            - run: pnpm lint:fix
            - run: pnpm build
```

### Package Management

```json
{
    "packageManager": "pnpm@10.11.0",
    "scripts": {
        "lint": "eslint .",
        "lint:fix": "eslint . --fix",
        "build": "rimraf dist && tsup && tsc --project ./tsconfig.build.json",
        "preinstall": "npx only-allow pnpm"
    }
}
```

## Migration Guide

### From Legacy ESLint Config

1. **Update package.json dependencies**:

    ```bash
    pnpm remove @typescript-eslint/eslint-plugin @typescript-eslint/parser
    pnpm add -D @kdt310722/eslint-config
    ```

2. **Replace eslint.config.js**:

    ```javascript
    export default import('@kdt310722/eslint-config').then((m) => m.defineFlatConfig())
    ```

3. **Update TypeScript config**:

    ```json
    {
        "extends": "@kdt310722/tsconfig"
    }
    ```

4. **Run auto-fix**:
    ```bash
    pnpm lint:fix
    ```

### Common Migration Issues

-   **Import organization**: Automatic sorting may change import order
-   **Trailing commas**: ES5 style enforced for objects/arrays
-   **Padding lines**: Additional blank lines added around statements
-   **Type imports**: Converted to type-only imports where applicable

---

_This comprehensive style guide is enforced through automated tooling and should be followed consistently across all projects. The rules are designed to ensure code readability, maintainability, and consistency across the entire codebase._
