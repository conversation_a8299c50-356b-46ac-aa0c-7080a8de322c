# Code Style Guide

## Table of Contents

- [Overview](#overview)
- [Formatting Rules](#formatting-rules)
- [Naming Conventions](#naming-conventions)
- [Import Organization](#import-organization)
- [Code Structure](#code-structure)
- [TypeScript Patterns](#typescript-patterns)
- [Custom ESLint Rules](#custom-eslint-rules)
- [Project-Specific Rules](#project-specific-rules)
- [Tools and Configuration](#tools-and-configuration)

## Overview

Comprehensive code style standards enforced through `@kdt310722/eslint-config` and `@kdt310722/tsconfig`.

**Base Configuration:**
```javascript
export default import('@kdt310722/eslint-config').then((m) => m.defineFlatConfig())
```

**Key Rule Categories:**
- Stylistic rules via `@stylistic/eslint-plugin`
- TypeScript strict checking via `@typescript-eslint`
- Import organization via `eslint-plugin-import-x`
- Custom formatting rules via `kdt/*` plugins

## Formatting Rules

### Indentation and Spacing
- **Indent**: 4 spaces (no tabs)
- **Line Width**: No enforced max width (`printWidth: false`)
- **End of Line**: Unix (LF)
- **Block Spacing**: Enabled
- **Bracket Same Line**: false (closing bracket on new line)

### Quotes and Semicolons
- **Quotes**: Single quotes for strings
- **JSX Quotes**: Double quotes for JSX attributes
- **Semicolons**: No semicolons (`semi: false`)
- **Quote Props**: Consistent quoting for object properties

### Braces and Commas
- **Brace Style**: 1tbs (one true brace style)
- **Comma Dangle**: ES5 style
  - Always for arrays/objects
  - Never for imports/exports
  - Never for function parameters
- **Arrow Parens**: Always use parentheses around arrow function parameters

### Padding Lines Between Statements
- **Always blank line before and after**: `class`, `interface`, `function`, `if`, `for`, `while`, `switch`, `try`
- **Always blank line before**: `return` statements
- **No blank lines between**: `case` statements in `switch`

### Object and Array Formatting

```typescript
// Single line for simple objects
const simple = { a: 1, b: 2 }

// Multi-line for complex objects (enforced by custom rule)
const complex = {
    database: {
        host: 'localhost',
        port: 5432,
    },
    cache: {
        ttl: 3600,
    },
}

// Consistent trailing commas
const array = ['item1', 'item2', 'item3']
```

## Naming Conventions

### Variables and Functions
- **camelCase** for variables and functions
- **Arrow functions** preferred for simple operations
- **Function declarations** for complex logic

### Constants
- **SCREAMING_SNAKE_CASE** for module-level constants
- **BigInt suffix** for large number constants

```typescript
export const MAX_VALID_SLOT_GAP = 100
export const SOL_DECIMALS = 9
export const PUMPFUN_TOKEN_PRICE_DECIMALS = 9n
```

### Types and Interfaces
- **PascalCase** for types, interfaces, and classes
- **Descriptive names** that indicate purpose
- **Utility types** preferred over complex inline types

### Files and Directories
- **kebab-case** for file names
- **camelCase** for TypeScript files when representing classes
- **Descriptive directory names** grouped by functionality

## Import Organization

### Import Grouping Order
1. **Node.js built-in modules** (with `node:` prefix)
2. **External libraries** (alphabetical)
3. **Side-effect imports** (`import 'module'`)
4. **Internal modules** (by proximity: `../`, `./`)

```typescript
// 1. Node.js built-ins
import { existsSync } from 'node:fs'
import process from 'node:process'

// 2. External libraries
import { DataSource } from 'typeorm'
import { tap } from '@kdt310722/utils/function'

// 3. Side-effects
import 'reflect-metadata'

// 4. Internal modules
import { config } from '../config'
import { logger } from './logger'
```

### Import Rules
- **Type-only imports**: Use `import type` for types only
- **Inline type imports**: Prefer `import { type Type, value }` when mixing
- **Single line imports**: Enforced by custom rule
- **No unused imports**: Automatically removed
- **Consistent sorting**: Alphabetical within groups

## Code Structure

### File Organization
1. **Imports** (following grouping rules)
2. **Type definitions** and interfaces
3. **Constants** and configuration
4. **Implementation** (functions, classes)
5. **Exports** (prefer named exports)

### Function Structure
- **Return types**: Always specified for public functions
- **Parameter types**: Always explicit
- **Complex signatures**: Use type aliases for readability

```typescript
// Simple function
export const createChildLogger = (name: string): Logger => logger.child({ name })

// Complex function with utility types
export function calculateTokenOut(
    bondingCurve: Pick<BondingCurve, 'virtualSolReserves' | 'virtualTokenReserves'>,
    solIn: bigint,
    feeBasisPoints: bigint
): bigint
```

### Class Organization
1. **Primary key** (for entities)
2. **Indexed columns** (for entities)
3. **Regular properties** (public → protected → private)
4. **Relationships** (for entities)
5. **Timestamps** (for entities)
6. **Constructor**
7. **Static methods**
8. **Instance methods** (public → protected → private)

## TypeScript Patterns

### Type Safety Rules
- **Strict TypeScript configuration** required
- **Explicit member accessibility** (`public`, `private`, `protected`)
- **No `any` type** (prefer `unknown`)
- **Utility types** for type manipulation (`Pick`, `Omit`, `Partial`)

### Type Imports and Exports
- **Consistent type imports**: `@typescript-eslint/consistent-type-imports`
- **Consistent type exports**: `@typescript-eslint/consistent-type-exports`
- **No import type side effects**: `@typescript-eslint/no-import-type-side-effects`

### Variable Naming Patterns
- **Unused parameters**: Prefix with underscore (`_error`, `_unused`)
- **Destructuring ignores**: Use underscore for ignored values
- **Rest parameters**: Use descriptive names or underscore

### BigInt Usage (Solana-specific)
- **BigInt literals**: Use `n` suffix for large numbers
- **Calculations**: All Solana amounts use BigInt
- **Constants**: BigInt for token decimals and denominators

```typescript
export const SOL_DENOMINATOR = 10 ** SOL_DECIMALS
export const PUMPFUN_TOKEN_PRICE_DECIMALS = 9n

// BigInt calculations
const inputAmount = (solIn * 10_000n) / (totalFeeBasisPoints + 10_000n)
```

### Database Entity Patterns (TypeORM)

```typescript
@Entity()
export class BuyTransaction {
    @PrimaryGeneratedColumn()
    public declare id: number

    @Index()
    @Column()
    public declare slot: number

    @BigIntColumn()
    public declare amount: bigint

    @JsonColumn()
    public declare tokenInfo: Token

    @CreateDateColumn()
    public declare createdAt: Date

    public static fromBuyResult(data: BuyData): BuyTransaction {
        const entity = new BuyTransaction()
        // Property assignment
        return entity
    }
}
```

## Custom ESLint Rules

### kdt/arrow-empty-body-newline
Prevents newlines in empty arrow function bodies.

```typescript
// ❌ Incorrect
const emptyFn = () => {
}

// ✅ Correct
const emptyFn = () => {}
```

### kdt/import-single-line
Enforces single-line import statements.

```typescript
// ❌ Incorrect
import {
    someFunction,
    anotherFunction
} from 'module'

// ✅ Correct
import { someFunction, anotherFunction } from 'module'
```

### kdt/object-curly-newline
Enhanced object formatting with minimum properties threshold.

```typescript
// Objects with fewer properties stay single line
const small = { a: 1, b: 2 }

// Objects exceeding threshold use multi-line format
const large = {
    property1: 'value1',
    property2: 'value2',
    property3: 'value3',
}
```

## Project-Specific Rules

### Common Rule Overrides

**farmer, json-rpc-proxy, solana-transaction-sender:**
- `'ts/prefer-promise-reject-errors': 'off'` - Allow non-Error rejections

**pumpfun-sdk, solana-grpc-client:**
- `'sonarjs/no-selector-parameter': 'off'` - Allow selector parameters

**solana-transaction-sender:**
- `'sonarjs/no-dead-store': 'off'` - Allow unused assignments

### File Ignore Patterns
- `migrations` - Database migrations
- `generated` - Auto-generated code
- `dist` - Build output
- `**/protos` - Protocol buffer definitions
- `README.md` - Documentation files

### TypeScript Configuration Variations

**Base projects:**
```json
{
    "extends": "@kdt310722/tsconfig"
}
```

**TypeORM projects:**
```json
{
    "extends": "@kdt310722/tsconfig",
    "compilerOptions": {
        "emitDecoratorMetadata": true,
        "experimentalDecorators": true
    }
}
```

## Tools and Configuration

### Required Dependencies
- **ESLint**: `@kdt310722/eslint-config`
- **TypeScript**: `@kdt310722/tsconfig`
- **Package Manager**: `pnpm` (enforced via `only-allow`)

### Git Hooks Setup
```json
{
    "simple-git-hooks": {
        "commit-msg": "npx --no -- commitlint --edit ${1}",
        "pre-commit": "npx lint-staged"
    },
    "lint-staged": {
        "*": "eslint --fix"
    }
}
```

### Build Configuration
- **tsup**: For library builds with dual CJS/ESM output
- **tsx**: For development execution
- **TypeORM**: For database entities with decorators

### IDE Settings
- **ESLint auto-fix**: Enable on save
- **TypeScript strict mode**: Required
- **Line endings**: LF (Unix)
- **Tab size**: 4 spaces
- **Trim trailing whitespace**: Enabled

---

*This style guide is enforced through automated tooling and should be followed consistently across all projects.*